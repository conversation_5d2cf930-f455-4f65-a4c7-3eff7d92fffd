CREATE TABLE "user" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "username" varchar UNIQUE NOT NULL,
  "password" varchar NOT NULL,
  "name" varchar NOT NULL,
  "dependent" varchar,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "role" (
  "id" SERIAL UNIQUE PRIMARY KEY NOT NULL,
  "name" varchar UNIQUE NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "user_role" (
  "id" SERIAL UNIQUE PRIMARY KEY NOT NULL,
  "user_id" int8 NOT NULL,
  "role_id" int4 NOT NULL
);

CREATE TABLE "statutory" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "title" varchar NOT NULL,
  "rate" float NOT NULL,
  "addamount_per_site" float NOT NULL,
  "addamount_per_hour" float NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "customer" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "name" varchar NOT NULL,
  "furigana" varchar NOT NULL,
  "post_code" varchar NOT NULL,
  "address_prefecture" varchar NOT NULL,
  "address_city" varchar NOT NULL,
  "address_building" varchar NOT NULL,
  "telephone" varchar NOT NULL,
  "fax" varchar NOT NULL,
  "billing_date" date NOT NULL,
  "statutory_id" int8 NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "transfer_destination" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar NOT NULL,
  "bank_name" varchar NOT NULL,
  "branch" varchar NOT NULL,
  "account_number" varchar NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "department" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "customer_id" int8 NOT NULL,
  "name" varchar NOT NULL,
  "transfer_destination_id" int8 NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "department_pic" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "department_id" int8 NOT NULL,
  "pic_belong" varchar NOT NULL,
  "pic_name" varchar NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "distant_fee" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "title" varchar NOT NULL,
  "explanation" text NOT NULL,
  "unit" float NOT NULL,
  "add_amount_per_hour" float NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "daily_report_addition" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "title" varchar NOT NULL,
  "explanation" text NOT NULL,
  "amount_per_site" float NOT NULL,
  "amount_per_hour" float NOT NULL,
  "welfare_subject" varchar NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "qualification" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "title" varchar NOT NULL,
  "explanation" text NOT NULL,
  "add_claim" float NOT NULL,
  "paid_amount" float NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "basic_price" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "title" varchar NOT NULL,
  "explanation" text NOT NULL,
  "price_json" json,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "distant_travel_allowance" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "title" varchar NOT NULL,
  "explanation" text NOT NULL,
  "add_claim" float NOT NULL,
  "add_paid" float NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "option" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "title" varchar NOT NULL,
  "explanation" text NOT NULL,
  "add_claim" float NOT NULL,
  "paid_amount" float NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "income_tax" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "more_than" float NOT NULL,
  "less_than" float NOT NULL,
  "amount" json,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "consumption_tax" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "start_date" date NOT NULL,
  "rate" float NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "block" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "name" varchar NOT NULL,
  "unit_price" float NOT NULL,
  "price_per_worker" float NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "district" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "block_id" int8 NOT NULL,
  "code" varchar UNIQUE NOT NULL,
  "name" varchar NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "site_report" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "work_date" date NOT NULL,
  "site_name" varchar NOT NULL,
  "department_pic_id" int8 NOT NULL,
  "has_statutory" bool NOT NULL,
  "bill_date" date NOT NULL,
  "basic_price_id" int8 NOT NULL,
  "daily_report_addition_id" int8 NOT NULL,
  "worker" int4 NOT NULL,
  "district_block_id" int8 NOT NULL,
  "district_block_unit" int2 NOT NULL,
  "transit_place_block_id" int8 NOT NULL,
  "transit_place_unit" int2 NOT NULL,
  "b_start_time" time NOT NULL,
  "b_end_time" time NOT NULL,
  "s_start_time" time NOT NULL,
  "s_end_time" time NOT NULL,
  "break_time" time,
  "late_early_worker" int4 NOT NULL,
  "extra_time_charge" json NOT NULL DEFAULT '[]',
  "note" text NOT NULL,
  "note_for_invoice" text NOT NULL,
  "total_amount" float NOT NULL,
  "consumption_tax_id" int8 NOT NULL,
  "snapshot" json NOT NULL DEFAULT '{}',
  "is_locked" bool NOT NULL,
  "is_invoice_issued" bool NOT NULL,
  "created_by" int8 NOT NULL,
  "created_at" timestamptz NOT NULL,
  "updated_at" timestamptz NOT NULL,
  "deleted_at" timestamptz
);

CREATE TABLE "site_report_statutory" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "site_report_id" int8 NOT NULL,
  "expense_per_worker" float NOT NULL,
  "adjustment" float NOT NULL,
  "total_amount" float NOT NULL,
  "snapshot" json NOT NULL DEFAULT '{}',
  "is_statutory_issued" bool NOT NULL,
  "note" text NOT NULL
);

CREATE TABLE "site_report_option" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "site_report_id" int8 NOT NULL,
  "option_id" int8 NOT NULL,
  "snapshot" json NOT NULL DEFAULT '{}'
);

CREATE TABLE "site_report_worker" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "site_report_id" int8 NOT NULL,
  "user_id" int8 NOT NULL,
  "start_time" time NOT NULL,
  "end_time" time NOT NULL,
  "break_time" time,
  "transportation_expense" float NOT NULL,
  "leader_allowance" float NOT NULL,
  "distant_fee_id" int8,
  "income_tax_id" int8 NOT NULL,
  "tax" float NOT NULL,
  "amount" float NOT NULL,
  "snapshot" json NOT NULL DEFAULT '{}',
  "status" varchar NOT NULL,
  "issued_date" date
);

CREATE TABLE "site_report_worker_qualification" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "site_report_worker_id" int8 NOT NULL,
  "qualification_id" int8 NOT NULL,
  "snapshot" json NOT NULL DEFAULT '{}'
);

ALTER TABLE "user_role" ADD FOREIGN KEY ("user_id") REFERENCES "user" ("id");

ALTER TABLE "user_role" ADD FOREIGN KEY ("role_id") REFERENCES "role" ("id");

ALTER TABLE "customer" ADD FOREIGN KEY ("statutory_id") REFERENCES "statutory" ("id");

ALTER TABLE "department" ADD FOREIGN KEY ("customer_id") REFERENCES "customer" ("id");

ALTER TABLE "department" ADD FOREIGN KEY ("transfer_destination_id") REFERENCES "transfer_destination" ("id");

ALTER TABLE "department_pic" ADD FOREIGN KEY ("department_id") REFERENCES "department" ("id");

ALTER TABLE "district" ADD FOREIGN KEY ("block_id") REFERENCES "block" ("id");

ALTER TABLE "site_report" ADD FOREIGN KEY ("department_pic_id") REFERENCES "department_pic" ("id");

ALTER TABLE "site_report" ADD FOREIGN KEY ("basic_price_id") REFERENCES "basic_price" ("id");

ALTER TABLE "site_report" ADD FOREIGN KEY ("daily_report_addition_id") REFERENCES "daily_report_addition" ("id");

ALTER TABLE "site_report" ADD FOREIGN KEY ("district_block_id") REFERENCES "block" ("id");

ALTER TABLE "site_report" ADD FOREIGN KEY ("transit_place_block_id") REFERENCES "block" ("id");

ALTER TABLE "site_report" ADD FOREIGN KEY ("consumption_tax_id") REFERENCES "consumption_tax" ("id");

ALTER TABLE "site_report" ADD FOREIGN KEY ("created_by") REFERENCES "user" ("id");

ALTER TABLE "site_report_statutory" ADD FOREIGN KEY ("site_report_id") REFERENCES "site_report" ("id");

ALTER TABLE "site_report_option" ADD FOREIGN KEY ("site_report_id") REFERENCES "site_report" ("id");

ALTER TABLE "site_report_option" ADD FOREIGN KEY ("option_id") REFERENCES "option" ("id");

ALTER TABLE "site_report_worker" ADD FOREIGN KEY ("site_report_id") REFERENCES "site_report" ("id");

ALTER TABLE "site_report_worker" ADD FOREIGN KEY ("user_id") REFERENCES "user" ("id");

ALTER TABLE "site_report_worker" ADD FOREIGN KEY ("distant_fee_id") REFERENCES "distant_fee" ("id");

ALTER TABLE "site_report_worker" ADD FOREIGN KEY ("income_tax_id") REFERENCES "income_tax" ("id");

ALTER TABLE "site_report_worker_qualification" ADD FOREIGN KEY ("site_report_worker_id") REFERENCES "site_report_worker" ("id");

ALTER TABLE "site_report_worker_qualification" ADD FOREIGN KEY ("qualification_id") REFERENCES "qualification" ("id");
