package sitereport

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	basicpriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	distantfeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
	incometaxDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/income_tax"
	qualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	sitereportworkerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker"
	sitereportworkerqualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker_qualification"
	statutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
	dbLib "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/database"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

func (uc *SiteReportUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get site reports from domain
	param := sitereportDmn.GetListParam{
		StartDate: req.ParsedStartDate,
		EndDate:   req.ParsedEndDate,
	}

	reports, err := uc.sitereport.GetList(ctx, param)
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(reports) == 0 {
		return []GetListResp{}, nil
	}

	// Aggregate data hierarchically
	return aggregateReports(reports), nil
}

// aggregateReports organizes site reports into hierarchical structure by year -> month -> date
func aggregateReports(reports []sitereportDmn.SiteReport) []GetListResp {
	// Group by year -> month -> date
	yearMap := make(map[int]map[int]map[int][]sitereportDmn.SiteReport)

	for _, report := range reports {
		year := report.WorkDate.Year()
		month := int(report.WorkDate.Month())
		day := report.WorkDate.Day()

		if yearMap[year] == nil {
			yearMap[year] = make(map[int]map[int][]sitereportDmn.SiteReport)
		}
		if yearMap[year][month] == nil {
			yearMap[year][month] = make(map[int][]sitereportDmn.SiteReport)
		}
		if yearMap[year][month][day] == nil {
			yearMap[year][month][day] = []sitereportDmn.SiteReport{}
		}

		yearMap[year][month][day] = append(yearMap[year][month][day], report)
	}

	// Convert to response structure with proper sorting
	var result []GetListResp

	// Sort years
	var years []int
	for year := range yearMap {
		years = append(years, year)
	}
	sortInts(years)

	for _, year := range years {
		monthMap := yearMap[year]
		yearResp := GetListResp{
			Year:  fmt.Sprintf("%d年", year),
			Month: []MonthData{},
		}

		// Sort months
		var months []int
		for month := range monthMap {
			months = append(months, month)
		}
		sortInts(months)

		for _, month := range months {
			dayMap := monthMap[month]
			monthData := MonthData{
				Value:       fmt.Sprintf("%02d月", month),
				Worker:      0,
				TotalAmount: 0,
				Date:        []DateData{},
			}

			// Sort days
			var days []int
			for day := range dayMap {
				days = append(days, day)
			}
			sortInts(days)

			for _, day := range days {
				dayReports := dayMap[day]
				dateData := DateData{
					Value:       fmt.Sprintf("%d日", day),
					Worker:      0,
					TotalAmount: 0,
					Report:      []ReportData{},
				}

				// Process reports for this date
				for _, report := range dayReports {
					reportData := ReportData{
						SiteReportID: report.ID,
						SiteName:     report.SiteName,
						Worker:       report.Worker,
						TotalAmount:  report.TotalAmount,
						BStartTime:   report.BStartTime.Format("15:04"),
						BEndTime:     report.BEndTime.Format("15:04"),
						Note:         report.Note,
						IsLocked:     report.IsLocked,
					}

					dateData.Report = append(dateData.Report, reportData)
					dateData.Worker += report.Worker
					dateData.TotalAmount += report.TotalAmount
				}

				monthData.Date = append(monthData.Date, dateData)
				monthData.Worker += dateData.Worker
				monthData.TotalAmount += dateData.TotalAmount
			}

			yearResp.Month = append(yearResp.Month, monthData)
		}

		result = append(result, yearResp)
	}

	return result
}

// sortInts sorts a slice of integers in ascending order
func sortInts(slice []int) {
	for i := 0; i < len(slice)-1; i++ {
		for j := i + 1; j < len(slice); j++ {
			if slice[i] > slice[j] {
				slice[i], slice[j] = slice[j], slice[i]
			}
		}
	}
}

// BulkUpdate handles bulk updating of site reports with role-based authorization
func (uc *SiteReportUseCase) BulkUpdate(ctx context.Context, req BulkUpdateReq) error {
	// Convert request to domain parameter
	param := sitereportDmn.BulkUpdateParam{
		SiteReportIDs:   req.SiteReportIDs,
		IsLocked:        req.IsLocked,
		IsInvoiceIssued: req.IsInvoiceIssued,
	}

	// Parse work_date if provided
	if req.WorkDate != nil {
		workDate, err := time.Parse("2006-01-02", *req.WorkDate)
		if err != nil {
			return log.LogError(ErrInvalidDateFormat, nil)
		}
		param.WorkDate = &workDate
	}

	// Call domain layer
	err := uc.sitereport.BulkUpdate(ctx, param)
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// GetStatutoryCalculationVariable calculates statutory calculation variables
func (uc *SiteReportUseCase) GetStatutoryCalculationVariable(ctx context.Context, req GetStatutoryCalculationVariableReq) (GetStatutoryCalculationVariableResp, error) {
	var resp GetStatutoryCalculationVariableResp

	// Run calculations concurrently using errgroup for efficiency
	var (
		statutoryRate    float64
		expensePerWorker float64
		addFeePerSite    float64
		addFeePerWorker  float64
	)

	g, egCtx := errgroup.WithContext(ctx)

	// Calculate statutory_rate
	g.Go(func() error {
		var err error
		statutoryRate, err = uc.calculateStatutoryRate(egCtx, req.CustomerID)
		return err
	})

	// Calculate expense_per_worker
	g.Go(func() error {
		var err error
		expensePerWorker, err = uc.calculateExpensePerWorker(egCtx, CalculateExpensePerWorker{
			BasicPriceID: req.BasicPriceID,
			StartTime:    req.ParsedStartTime,
			EndTime:      req.ParsedEndTime,
			BreakTime:    req.ParsedBreakTime,
		})
		return err
	})

	// Calculate add_fee_per_site and add_fee_per_worker
	g.Go(func() error {
		var err error
		addFeePerSite, addFeePerWorker, err = uc.calculateAddFees(egCtx, req.DailyReportAdditionID)
		return err
	})

	// Wait for all calculations to complete
	if err := g.Wait(); err != nil {
		return GetStatutoryCalculationVariableResp{}, log.LogError(err, nil)
	}

	// Assign results to response
	resp.StatutoryRate = statutoryRate
	resp.ExpensePerWorker = expensePerWorker
	resp.AddFeePerSite = addFeePerSite
	resp.AddFeePerWorker = addFeePerWorker

	return resp, nil
}

// WorkerCalculation calculates worker payment including all allowances and taxes
func (uc *SiteReportUseCase) WorkerCalculation(ctx context.Context, req WorkerCalculationReq) (WorkerCalculationResp, error) {
	resp, err := uc.DoWorkerCalculation(ctx, req)
	if err != nil {
		return WorkerCalculationResp{}, log.LogError(err, nil)
	}

	return WorkerCalculationResp{
		TotalAmount:         resp.TotalAmount,
		TaxAmount:           resp.TaxAmount,
		TotalAmountAfterTax: resp.TotalAmountAfterTax,
	}, nil
}

func (uc *SiteReportUseCase) DoWorkerCalculation(ctx context.Context, req WorkerCalculationReq) (DoWorkerCalculationResp, error) {
	var (
		resp                 DoWorkerCalculationResp
		baseSalary           float64
		distantFee           float64
		qualificationFeeResp CalculateQualificationFeeResp
	)

	// Use errgroup to perform calculations concurrently
	g, gctx := errgroup.WithContext(ctx)

	// Calculate base salary
	g.Go(func() error {
		var err error
		baseSalary, err = uc.calculateExpensePerWorker(gctx, CalculateExpensePerWorker{
			BasicPriceID: req.BasicPriceID,
			StartTime:    req.ParsedStartTime,
			EndTime:      req.ParsedEndTime,
			BreakTime:    req.ParsedBreakTime,
		})
		return err
	})

	// Calculate distant fee if provided
	g.Go(func() error {
		var err error
		distantFee, err = uc.calculateDistantFee(gctx, req.DistantFeeID, req.ParsedStartTime, req.ParsedEndTime, req.ParsedBreakTime)
		return err
	})

	// Calculate qualification fees if provided
	g.Go(func() error {
		var err error
		qualificationFeeResp, err = uc.calculateQualificationFee(gctx, req.QualificationAllowanceIDs)
		if err != nil {
			return err
		}
		return err
	})

	// Wait for all calculations to complete
	if err := g.Wait(); err != nil {
		return DoWorkerCalculationResp{}, log.LogError(err, nil)
	}

	// Calculate total amount
	totalAmount := baseSalary + req.TransportExpense + req.LeaderAllowance + distantFee + qualificationFeeResp.TotalFee

	// Calculate tax amount and get income tax ID
	taxAmountResp, err := uc.calculateTaxAmount(ctx, CalculateTaxAmountParam{
		TotalAmount: totalAmount,
		UserID:      req.UserID,
	})
	if err != nil {
		return DoWorkerCalculationResp{}, log.LogError(err, nil)
	}

	// Calculate total amount after tax
	totalAmountAfterTax := totalAmount - taxAmountResp.Amount

	// Assign results to response
	resp.TotalAmount = totalAmount
	resp.TaxIncomeID = taxAmountResp.IncomeTaxID
	resp.TaxAmount = taxAmountResp.Amount
	resp.TotalAmountAfterTax = totalAmountAfterTax
	resp.DistantFee = distantFee
	resp.Qualifications = qualificationFeeResp.Qualifications

	return resp, nil
}

// calculateDistantFee calculates distant fee based on working hours
func (uc *SiteReportUseCase) calculateDistantFee(ctx context.Context, distantFeeID int64, startTime, endTime time.Time, breakTime *time.Time) (float64, error) {
	if distantFeeID <= 0 {
		return 0, nil
	}

	// Get distant fee record
	distantFee, err := uc.distantfee.GetByID(ctx, distantfeeDmn.GetByIDParam{
		ID: distantFeeID,
	})
	if err != nil {
		return 0, err
	}
	if distantFee.ID == 0 {
		return 0, errors.New("distant fee not found")
	}

	// Calculate working hours
	workingHours := uc.calculateWorkingHours(startTime, endTime, breakTime)

	// Calculate distant fee: unit * add_amount_per_hour * working_hours
	distantFeeAmount := distantFee.Unit * distantFee.AddAmountPerHour * workingHours

	return distantFeeAmount, nil
}

// calculateQualificationFee calculates total qualification fees
func (uc *SiteReportUseCase) calculateQualificationFee(ctx context.Context, qualificationIDs []int64) (CalculateQualificationFeeResp, error) {
	if len(qualificationIDs) == 0 {
		return CalculateQualificationFeeResp{}, nil
	}

	// Get qualification records
	qualifications, err := uc.qualification.GetByIDs(ctx, qualificationDmn.GetByIDsParam{
		IDs: qualificationIDs,
	})
	if err != nil {
		return CalculateQualificationFeeResp{}, err
	}

	// Sum all add_claim values and store in Qualification struct
	var totalFee float64
	qualificationRes := make([]Qualification, len(qualifications))
	for i, qualification := range qualifications {
		totalFee += qualification.AddClaim

		qualificationRes[i] = Qualification{
			ID:       qualification.ID,
			Title:    qualification.Title,
			AddClaim: qualification.AddClaim,
		}
	}

	return CalculateQualificationFeeResp{
		TotalFee:       totalFee,
		Qualifications: qualificationRes,
	}, nil
}

// calculateTaxAmount calculates tax amount based on total amount and user dependency type
func (uc *SiteReportUseCase) calculateTaxAmount(ctx context.Context, param CalculateTaxAmountParam) (CalculateTaxAmountResp, error) {
	// Get user information to determine dependency type
	user, err := uc.user.GetByID(ctx, param.UserID)
	if err != nil {
		return CalculateTaxAmountResp{}, err
	}
	if user.ID == 0 {
		return CalculateTaxAmountResp{}, log.LogError(fmt.Errorf("user not found with ID: %d", param.UserID), nil)
	}

	// Return error if no dependency type is set
	if user.Dependent == nil || *user.Dependent == "" {
		return CalculateTaxAmountResp{}, log.LogError(fmt.Errorf("user dependency type not found with ID: %d", param.UserID), nil)
	}
	dependentType := *user.Dependent

	// Get income tax record
	incomeTax, err := uc.incomeTax.GetByAmount(ctx, incometaxDmn.GetByAmountParam{
		Amount: param.TotalAmount,
	})
	if err != nil {
		return CalculateTaxAmountResp{}, err
	}
	if incomeTax.ID == 0 {
		// No tax applicable
		return CalculateTaxAmountResp{}, nil
	}

	// Get the specific tax amount for the user's dependency type
	taxAmount, err := getTaxAmountByType(incomeTax, dependentType)
	if err != nil {
		return CalculateTaxAmountResp{}, err
	}

	return CalculateTaxAmountResp{
		IncomeTaxID: incomeTax.ID,
		Amount:      taxAmount,
	}, nil
}

// GetTaxAmountByType extracts the tax amount for a specific dependency type from the JSON amount field.
func getTaxAmountByType(incomeTax incometaxDmn.IncomeTax, dependentType string) (float64, error) {
	if incomeTax.Amount == "" {
		return 0, nil
	}

	var taxAmounts []incometaxDmn.TaxAmountItem
	err := json.Unmarshal([]byte(incomeTax.Amount), &taxAmounts)
	if err != nil {
		return 0, log.LogError(err, nil)
	}

	// Find the tax amount for the specified dependency type
	for _, item := range taxAmounts {
		if item.Type == dependentType {
			return item.Price, nil
		}
	}

	// If dependency type not found, return 0
	return 0, nil
}

// SaveWorker handles saving site report worker data with business logic
func (uc *SiteReportUseCase) SaveWorker(ctx context.Context, req SaveWorkerReq) error {
	// Determine operation based on ID
	if req.ID < 0 {
		// Delete operation
		return uc.deleteWorker(ctx, -req.ID)
	} else if req.ID > 0 {
		// Update operation
		return uc.updateWorker(ctx, req)
	}

	// Invalid ID
	return errors.New("invalid ID: must be positive for update or negative for delete")
}

// deleteWorker handles deleting a site report worker record
func (uc *SiteReportUseCase) deleteWorker(ctx context.Context, workerID int64) error {
	tx := dbLib.InitTx()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Delete worker qualifications first
	err := uc.sitereportworkerqualification.BulkDeleteWithTx(ctx, tx, sitereportworkerqualificationDmn.BulkDeleteParam{
		SiteReportWorkerID: workerID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Delete the worker record
	err = uc.sitereportworker.DeleteWorkerWithTx(ctx, tx, sitereportworkerDmn.DeleteWorkerParam{
		ID: workerID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// updateWorker handles updating a site report worker record with calculations and snapshots
func (uc *SiteReportUseCase) updateWorker(ctx context.Context, req SaveWorkerReq) error {
	// Get user information for snapshot
	user, err := uc.user.GetByID(ctx, req.UserID)
	if err != nil {
		return log.LogError(err, nil)
	}
	if user.ID == 0 {
		return ErrUserNotFound
	}

	// Perform worker calculation
	calcReq := WorkerCalculationReq{
		UserID:                    req.UserID,
		BasicPriceID:              req.BasicPriceID,
		StartTime:                 req.StartTime,
		EndTime:                   req.EndTime,
		BreakTime:                 req.BreakTime,
		TransportExpense:          req.TransportExpense,
		LeaderAllowance:           req.LeaderAllowance,
		DistantFeeID:              req.DistantFeeID,
		QualificationAllowanceIDs: req.QualificationAllowanceIDs,
		ParsedStartTime:           req.ParsedStartTime,
		ParsedEndTime:             req.ParsedEndTime,
		ParsedBreakTime:           req.ParsedBreakTime,
	}

	calcResp, err := uc.DoWorkerCalculation(ctx, calcReq)
	if err != nil {
		return log.LogError(err, nil)
	}

	// Create worker snapshot
	workerSnapshotJSON, err := json.Marshal(sitereportworkerDmn.Snapshot{
		WorkerName: user.Name,
		DistantFee: calcResp.DistantFee,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Do update worker
	err = uc.doUpdateWorker(ctx, DoUpdateWorkerParam{
		Req:                req,
		CalcResp:           calcResp,
		WorkerSnapshotJSON: string(workerSnapshotJSON),
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// GetDetailList retrieves detailed site report information with related data
func (uc *SiteReportUseCase) GetDetailList(ctx context.Context, req GetDetailListReq) ([]GetDetailListResp, error) {
	// Convert request to domain parameter
	param := sitereportDmn.GetDetailListParam{}

	if req.WorkDate != "" {
		param.WorkDate = &req.ParsedWorkDate
	}

	if req.ID > 0 {
		param.ID = &req.ID
	}

	// Get site reports from domain
	reports, err := uc.sitereport.GetDetailList(ctx, param)
	if err != nil {
		return []GetDetailListResp{}, log.LogError(err, nil)
	}
	if len(reports) == 0 {
		return []GetDetailListResp{}, nil
	}

	// Convert to response format with detailed information
	return uc.buildDetailListResponse(ctx, reports)
}

// buildDetailListResponse builds the detailed response with all related data
func (uc *SiteReportUseCase) buildDetailListResponse(ctx context.Context, reports []sitereportDmn.SiteReport) ([]GetDetailListResp, error) {
	result := make([]GetDetailListResp, 0, len(reports))

	for _, report := range reports {
		resp, err := uc.buildSingleDetailResponse(ctx, report)
		if err != nil {
			return []GetDetailListResp{}, log.LogError(err, nil)
		}
		result = append(result, resp)
	}

	return result, nil
}

// buildSingleDetailResponse builds a single detailed response with all related data
func (uc *SiteReportUseCase) buildSingleDetailResponse(ctx context.Context, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	resp := GetDetailListResp{
		ID:                    report.ID,
		WorkDate:              report.WorkDate.Format("2006-01-02"),
		IsLocked:              report.IsLocked,
		SiteName:              report.SiteName,
		HasStatutory:          report.HasStatutory,
		DepartmentPicID:       report.DepartmentPicID,
		BillDate:              report.BillDate.Format("2006-01-02"),
		BasicPriceID:          report.BasicPriceID,
		DailyReportAdditionID: report.DailyReportAdditionID,
		TotalWorker:           report.Worker,
		DistrictBlockID:       report.DistrictBlockID,
		DistrictBlockUnit:     report.DistrictBlockUnit,
		TransitPlaceBlockID:   report.TransitPlaceBlockID,
		TransitPlaceBlockUnit: report.TransitPlaceUnit,
		BStartTime:            report.BStartTime.String(),
		BEndTime:              report.BEndTime.String(),
		SStartTime:            report.SStartTime.String(),
		SEndTime:              report.SEndTime.String(),
		BreakTime:             report.BreakTime.String(),
		LateEarlyWorker:       report.LateEarlyWorker,
		Note:                  report.Note,
		NoteForInvoice:        report.NoteForInvoice,
		TotalAmount:           report.TotalAmount,
	}

	// Parse extra_time_charge JSON
	var extraTimeCharge []ExtraTimeChargeItem
	err := json.Unmarshal([]byte(report.ExtraTimeCharge), &extraTimeCharge)
	if err != nil {
		return GetDetailListResp{}, log.LogError(err, nil)
	}
	resp.ExtraTimeCharge = extraTimeCharge

	// Handle related data based on is_locked status
	if report.IsLocked {
		// Use snapshot data
		resp, err = uc.populateFromSnapshot(resp, report)
		if err != nil {
			return GetDetailListResp{}, log.LogError(err, nil)
		}
	} else {
		// Fetch from related tables
		resp, err = uc.populateFromRelatedTables(resp, report)
		if err != nil {
			return GetDetailListResp{}, log.LogError(err, nil)
		}
	}

	// Fetch nested objects (statutory, option, worker)
	err = uc.populateNestedObjects(ctx, &resp, report)
	if err != nil {
		return GetDetailListResp{}, log.LogError(err, nil)
	}

	return resp, nil
}

// populateFromSnapshot populates response data from snapshot JSON for locked reports
func (uc *SiteReportUseCase) populateFromSnapshot(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	// Parse snapshot JSON
	var snapshot sitereportDmn.Snapshot
	if err := json.Unmarshal([]byte(report.Snapshot), &snapshot); err != nil {
		return GetDetailListResp{}, log.LogError(err, nil)
	}

	// Extract customer info
	resp.CustomerID = snapshot.CustomerID
	resp.CustomerName = snapshot.CustomerName

	// Extract department info
	resp.DepartmentID = snapshot.DepartmentID
	resp.DepartmentName = snapshot.DepartmentName

	// Extract department pic name
	resp.DepartmentPicName = snapshot.DepartmentPicName

	// Extract basic price name
	resp.BasicPriceName = snapshot.BasicPriceName

	// Extract daily report addition info
	resp.DailyReportAdditionName = snapshot.DailyReportAdditionName
	resp.DailyReportAdditionPerSite = snapshot.DailyReportAdditionPerSite
	resp.DailyReportAdditionPerWorker = snapshot.DailyReportAdditionPerWorker

	// Extract block names
	resp.DistrictBlockName = snapshot.DistrictBlockName
	resp.TransitPlaceBlockName = snapshot.TransitPlaceBlockName

	return resp, nil
}

// populateFromRelatedTables populates response data from preloaded relationships for unlocked reports
func (uc *SiteReportUseCase) populateFromRelatedTables(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	// Department PIC and related data (Department → Customer chain)
	if report.DepartmentPic.ID != 0 {
		resp.DepartmentPicName = report.DepartmentPic.PicName
		resp.DepartmentID = report.DepartmentPic.DepartmentID

		if report.DepartmentPic.Department.ID != 0 {
			resp.DepartmentName = report.DepartmentPic.Department.Name
			resp.CustomerID = report.DepartmentPic.Department.CustomerID

			if report.DepartmentPic.Department.Customer.ID != 0 {
				resp.CustomerName = report.DepartmentPic.Department.Customer.Name
			}
		}
	}

	// Basic price data
	if report.BasicPrice.ID != 0 {
		resp.BasicPriceName = report.BasicPrice.Title
	}

	// Daily report addition data
	if report.DailyReportAddition.ID != 0 {
		resp.DailyReportAdditionName = report.DailyReportAddition.Title
		resp.DailyReportAdditionPerSite = report.DailyReportAddition.AmountPerSite
		resp.DailyReportAdditionPerWorker = report.DailyReportAddition.AmountPerHour
	}

	// Block data
	if report.DistrictBlock.ID != 0 {
		resp.DistrictBlockName = report.DistrictBlock.Name
	}
	if report.TransitPlaceBlock.ID != 0 {
		resp.TransitPlaceBlockName = report.TransitPlaceBlock.Name
	}

	return resp, nil
}

// populateNestedObjects fetches and populates statutory, option, and worker data
func (uc *SiteReportUseCase) populateNestedObjects(ctx context.Context, resp *GetDetailListResp, report sitereportDmn.SiteReport) error {
	// Use errgroup for concurrent fetching
	g, gctx := errgroup.WithContext(ctx)

	// Fetch statutory data
	g.Go(func() error {
		return uc.fetchStatutoryData(gctx, resp, report)
	})

	// Fetch option data
	g.Go(func() error {
		return uc.fetchOptionData(gctx, resp, report)
	})

	// Fetch worker data
	g.Go(func() error {
		return uc.fetchWorkerData(gctx, resp, report)
	})

	// Wait for all fetches to complete
	if err := g.Wait(); err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// fetchStatutoryData fetches statutory information for the site report
func (uc *SiteReportUseCase) fetchStatutoryData(ctx context.Context, resp *GetDetailListResp, report sitereportDmn.SiteReport) error {
	if !report.HasStatutory {
		resp.Statutory = nil
		return nil
	}

	db := dbmanager.Manager().WithContext(ctx)

	if report.IsLocked {
		// Fetch from site_report_statutory with snapshot data
		var statutory struct {
			ID               int64   `gorm:"column:id"`
			ExpensePerWorker float64 `gorm:"column:expense_per_worker"`
			Adjustment       float64 `gorm:"column:adjustment"`
			TotalAmount      float64 `gorm:"column:total_amount"`
			Note             string  `gorm:"column:note"`
			Snapshot         string  `gorm:"column:snapshot"`
		}

		err := db.Table("site_report_statutory").
			Where("site_report_id = ?", report.ID).
			First(&statutory).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				resp.Statutory = nil
				return nil
			}
			return log.LogError(err, nil)
		}

		// Parse snapshot for rate
		var snapshot map[string]interface{}
		var rate float64
		if err := json.Unmarshal([]byte(statutory.Snapshot), &snapshot); err == nil {
			if r, ok := snapshot["rate"].(float64); ok {
				rate = r
			}
		}

		resp.Statutory = &GetDetailListStatutoryResp{
			ID:               statutory.ID,
			Rate:             rate,
			ExpensePerWorker: statutory.ExpensePerWorker,
			Adjustment:       statutory.Adjustment,
			TotalAmount:      statutory.TotalAmount,
			Note:             statutory.Note,
		}
	} else {
		// Fetch from site_report_statutory and join with statutory table for rate
		var statutory struct {
			ID               int64   `gorm:"column:id"`
			Rate             float64 `gorm:"column:rate"`
			ExpensePerWorker float64 `gorm:"column:expense_per_worker"`
			Adjustment       float64 `gorm:"column:adjustment"`
			TotalAmount      float64 `gorm:"column:total_amount"`
			Note             string  `gorm:"column:note"`
		}

		err := db.Table("site_report_statutory").
			Select("site_report_statutory.id, statutory.rate, site_report_statutory.expense_per_worker, site_report_statutory.adjustment, site_report_statutory.total_amount, site_report_statutory.note").
			Joins("INNER JOIN site_report ON site_report_statutory.site_report_id = site_report.id").
			Joins("INNER JOIN department_pic ON site_report.department_pic_id = department_pic.id").
			Joins("INNER JOIN department ON department_pic.department_id = department.id").
			Joins("INNER JOIN customer ON department.customer_id = customer.id").
			Joins("INNER JOIN statutory ON customer.statutory_id = statutory.id").
			Where("site_report_statutory.site_report_id = ?", report.ID).
			Where("statutory.deleted_at IS NULL").
			First(&statutory).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				resp.Statutory = nil
				return nil
			}
			return log.LogError(err, nil)
		}

		resp.Statutory = &GetDetailListStatutoryResp{
			ID:               statutory.ID,
			Rate:             statutory.Rate,
			ExpensePerWorker: statutory.ExpensePerWorker,
			Adjustment:       statutory.Adjustment,
			TotalAmount:      statutory.TotalAmount,
			Note:             statutory.Note,
		}
	}

	return nil
}

// fetchOptionData fetches option information for the site report
func (uc *SiteReportUseCase) fetchOptionData(ctx context.Context, resp *GetDetailListResp, report sitereportDmn.SiteReport) error {
	db := dbmanager.Manager().WithContext(ctx)

	if report.IsLocked {
		// Fetch from site_report_option with snapshot data
		var options []struct {
			ID       int64  `gorm:"column:id"`
			Snapshot string `gorm:"column:snapshot"`
		}

		err := db.Table("site_report_option").
			Select("id, snapshot").
			Where("site_report_id = ?", report.ID).
			Find(&options).Error
		if err != nil {
			return log.LogError(err, nil)
		}

		resp.Option = make([]GetDetailListOptionResp, 0, len(options))
		for _, option := range options {
			// Parse snapshot for name and amount
			var snapshot map[string]interface{}
			var name string
			var amount float64

			if err := json.Unmarshal([]byte(option.Snapshot), &snapshot); err == nil {
				if n, ok := snapshot["name"].(string); ok {
					name = n
				}
				if a, ok := snapshot["amount"].(float64); ok {
					amount = a
				}
			}

			resp.Option = append(resp.Option, GetDetailListOptionResp{
				ID:     option.ID,
				Name:   name,
				Amount: amount,
			})
		}
	} else {
		// Fetch from site_report_option and join with option table
		var options []struct {
			ID     int64   `gorm:"column:id"`
			Name   string  `gorm:"column:title"`
			Amount float64 `gorm:"column:add_claim"`
		}

		err := db.Table("site_report_option").
			Select("site_report_option.id, option.title, option.add_claim").
			Joins("INNER JOIN option ON site_report_option.option_id = option.id").
			Where("site_report_option.site_report_id = ?", report.ID).
			Where("option.deleted_at IS NULL").
			Find(&options).Error
		if err != nil {
			return log.LogError(err, nil)
		}

		resp.Option = make([]GetDetailListOptionResp, 0, len(options))
		for _, option := range options {
			resp.Option = append(resp.Option, GetDetailListOptionResp{
				ID:     option.ID,
				Name:   option.Name,
				Amount: option.Amount,
			})
		}
	}

	return nil
}

// fetchWorkerData fetches worker information for the site report
func (uc *SiteReportUseCase) fetchWorkerData(ctx context.Context, resp *GetDetailListResp, report sitereportDmn.SiteReport) error {
	db := dbmanager.Manager().WithContext(ctx)

	if report.IsLocked {
		// Fetch from site_report_worker with snapshot data
		var workers []struct {
			ID       int64  `gorm:"column:id"`
			Snapshot string `gorm:"column:snapshot"`
		}

		err := db.Table("site_report_worker").
			Select("id, snapshot").
			Where("site_report_id = ?", report.ID).
			Find(&workers).Error
		if err != nil {
			return log.LogError(err, nil)
		}

		resp.Worker = make([]GetDetailListWorkerResp, 0, len(workers))
		for _, worker := range workers {
			// Parse snapshot for worker name
			var snapshot map[string]interface{}
			var name string

			if err := json.Unmarshal([]byte(worker.Snapshot), &snapshot); err == nil {
				if n, ok := snapshot["worker_name"].(string); ok {
					name = n
				}
			}

			resp.Worker = append(resp.Worker, GetDetailListWorkerResp{
				ID:   worker.ID,
				Name: name,
			})
		}
	} else {
		// Fetch from site_report_worker and join with user table
		var workers []struct {
			ID   int64  `gorm:"column:id"`
			Name string `gorm:"column:name"`
		}

		err := db.Table("site_report_worker").
			Select("site_report_worker.id, user.name").
			Joins("INNER JOIN user ON site_report_worker.user_id = user.id").
			Where("site_report_worker.site_report_id = ?", report.ID).
			Where("user.deleted_at IS NULL").
			Find(&workers).Error
		if err != nil {
			return log.LogError(err, nil)
		}

		resp.Worker = make([]GetDetailListWorkerResp, 0, len(workers))
		for _, worker := range workers {
			resp.Worker = append(resp.Worker, GetDetailListWorkerResp{
				ID:   worker.ID,
				Name: worker.Name,
			})
		}
	}

	return nil
}

// doUpdateWorker handles the database transaction for updating worker and qualifications
func (uc *SiteReportUseCase) doUpdateWorker(ctx context.Context, param DoUpdateWorkerParam) error {
	tx := dbLib.InitTx()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update the worker record
	err := uc.sitereportworker.UpdateWorkerWithTx(ctx, tx, sitereportworkerDmn.UpdateWorkerParam{
		ID:                    param.Req.ID,
		UserID:                param.Req.UserID,
		StartTime:             param.Req.ParsedStartTime,
		EndTime:               param.Req.ParsedEndTime,
		BreakTime:             param.Req.ParsedBreakTime,
		TransportationExpense: param.Req.TransportExpense,
		LeaderAllowance:       param.Req.LeaderAllowance,
		DistantFeeID:          parse.ZeroInt64Pointer(param.Req.DistantFeeID),
		IncomeTaxID:           param.CalcResp.TaxIncomeID,
		Tax:                   param.CalcResp.TaxAmount,
		Amount:                param.CalcResp.TotalAmount,
		Snapshot:              param.WorkerSnapshotJSON,
	})
	if err != nil {
		tx.Rollback()
		return log.LogError(err, nil)
	}

	// Handle qualification management
	err = uc.manageWorkerQualifications(ctx, tx, ManageWorkerQualificationsParam{
		WorkerID:                  param.Req.ID,
		RequestedQualificationIDs: param.Req.QualificationAllowanceIDs,
		CalculatedQualifications:  param.CalcResp.Qualifications,
	})
	if err != nil {
		tx.Rollback()
		return log.LogError(err, nil)
	}

	err = tx.Commit().Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// manageWorkerQualifications handles the complex qualification management logic
func (uc *SiteReportUseCase) manageWorkerQualifications(ctx context.Context, tx *gorm.DB, param ManageWorkerQualificationsParam) error {
	// Get existing qualifications for this worker
	existingQualifications, err := uc.sitereportworkerqualification.GetByWorkerID(ctx, sitereportworkerqualificationDmn.GetByWorkerIDParam{
		SiteReportWorkerID: param.WorkerID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Create maps for easier comparison
	existingQualMap := make(map[int64]bool)
	for _, qual := range existingQualifications {
		existingQualMap[qual.QualificationID] = true
	}

	// Create maps for easier comparison
	requestedQualMap := make(map[int64]bool)
	for _, qualID := range param.RequestedQualificationIDs {
		requestedQualMap[qualID] = true
	}

	// Create qualification data map from calculated qualifications
	qualDataMap := make(map[int64]Qualification)
	for _, qual := range param.CalculatedQualifications {
		qualDataMap[qual.ID] = qual
	}

	// Process qualification changes
	err = uc.processQualificationChanges(ctx, tx, ProcessQualificationChangesParam{
		WorkerID:         param.WorkerID,
		ExistingQualMap:  existingQualMap,
		RequestedQualMap: requestedQualMap,
		QualDataMap:      qualDataMap,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// processQualificationChanges handles the bulk operations for qualification changes
func (uc *SiteReportUseCase) processQualificationChanges(ctx context.Context, tx *gorm.DB, param ProcessQualificationChangesParam) error {
	var toUpdate []sitereportworkerqualificationDmn.QualificationUpdateData
	var toInsert []sitereportworkerqualificationDmn.QualificationData
	var toDelete []int64

	// Determine operations needed
	for qualID := range param.RequestedQualMap {
		if param.ExistingQualMap[qualID] {
			// Update existing qualification
			if qualData, exists := param.QualDataMap[qualID]; exists {
				snapshot := sitereportworkerqualificationDmn.Snapshot{
					Title:    qualData.Title,
					AddClaim: qualData.AddClaim,
				}
				snapshotJSON, err := json.Marshal(snapshot)
				if err != nil {
					return log.LogError(err, nil)
				}
				toUpdate = append(toUpdate, sitereportworkerqualificationDmn.QualificationUpdateData{
					QualificationID: qualID,
					Snapshot:        string(snapshotJSON),
				})
			}
		} else {
			// Insert new qualification
			if qualData, exists := param.QualDataMap[qualID]; exists {
				snapshot := sitereportworkerqualificationDmn.Snapshot{
					Title:    qualData.Title,
					AddClaim: qualData.AddClaim,
				}
				snapshotJSON, err := json.Marshal(snapshot)
				if err != nil {
					return log.LogError(err, nil)
				}
				toInsert = append(toInsert, sitereportworkerqualificationDmn.QualificationData{
					QualificationID: qualID,
					Snapshot:        string(snapshotJSON),
				})
			}
		}
	}

	// Find qualifications to delete
	for qualID := range param.ExistingQualMap {
		if !param.RequestedQualMap[qualID] {
			toDelete = append(toDelete, qualID)
		}
	}

	// Execute bulk operations
	err := uc.executeBulkQualificationOperations(ctx, tx, ExecuteBulkQualificationOperationsParam{
		WorkerID: param.WorkerID,
		ToUpdate: toUpdate,
		ToInsert: toInsert,
		ToDelete: toDelete,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// executeBulkQualificationOperations performs the actual bulk database operations asynchronously
func (uc *SiteReportUseCase) executeBulkQualificationOperations(ctx context.Context, tx *gorm.DB, param ExecuteBulkQualificationOperationsParam) error {
	// Use errgroup for proper synchronization and error handling
	g, gctx := errgroup.WithContext(ctx)

	// Execute bulk update asynchronously
	if len(param.ToUpdate) > 0 {
		g.Go(func() error {
			err := uc.sitereportworkerqualification.BulkUpdateWithTx(gctx, tx, sitereportworkerqualificationDmn.BulkUpdateParam{
				SiteReportWorkerID: param.WorkerID,
				Qualifications:     param.ToUpdate,
			})
			if err != nil {
				return log.LogError(err, nil)
			}
			return nil
		})
	}

	// Execute bulk insert asynchronously
	if len(param.ToInsert) > 0 {
		g.Go(func() error {
			err := uc.sitereportworkerqualification.BulkInsertWithTx(gctx, tx, sitereportworkerqualificationDmn.BulkInsertParam{
				SiteReportWorkerID: param.WorkerID,
				Qualifications:     param.ToInsert,
			})
			if err != nil {
				return log.LogError(err, nil)
			}
			return nil
		})
	}

	// Execute bulk delete asynchronously
	if len(param.ToDelete) > 0 {
		g.Go(func() error {
			err := uc.sitereportworkerqualification.BulkDeleteWithTx(gctx, tx, sitereportworkerqualificationDmn.BulkDeleteParam{
				SiteReportWorkerID: param.WorkerID,
				QualificationIDs:   param.ToDelete,
			})
			if err != nil {
				return log.LogError(err, nil)
			}
			return nil
		})
	}

	// Wait for all operations to complete
	// If any operation fails, the context will be cancelled and remaining operations will stop
	if err := g.Wait(); err != nil {
		return err
	}

	return nil
}

// calculateWorkingHours calculates working hours excluding break time
func (uc *SiteReportUseCase) calculateWorkingHours(startTime, endTime time.Time, breakTime *time.Time) float64 {
	// Calculate total duration
	totalDuration := endTime.Sub(startTime)

	// Subtract 1 hour from end time as per requirement
	adjustedEndTime := endTime.Add(-1 * time.Hour)
	if adjustedEndTime.Before(startTime) {
		return 0
	}

	// Recalculate duration with adjusted end time
	totalDuration = adjustedEndTime.Sub(startTime)

	// Subtract break time if it falls within working hours
	if breakTime != nil {
		if breakTime.After(startTime) && breakTime.Before(adjustedEndTime) {
			// Break time is within working hours, subtract 1 hour
			totalDuration -= time.Hour
		}
	}

	// Convert to hours
	hours := totalDuration.Hours()
	if hours < 0 {
		return 0
	}

	return hours
}

// calculateStatutoryRate calculates the statutory rate for a customer
func (uc *SiteReportUseCase) calculateStatutoryRate(ctx context.Context, customerID int64) (float64, error) {
	statutory, err := uc.statutory.GetByCustomerID(ctx, statutoryDmn.GetByCustomerIDParam{
		CustomerID: customerID,
	})
	if err != nil {
		return 0, err
	}
	if statutory.ID == 0 {
		return 0, errors.New("法定が見つかりません")
	}

	return statutory.Rate, nil
}

// calculateExpensePerWorker calculates expense per worker from basic price JSON
func (uc *SiteReportUseCase) calculateExpensePerWorker(ctx context.Context, param CalculateExpensePerWorker) (float64, error) {
	basicPrice, err := uc.basicPrice.GetByID(ctx, basicpriceDmn.GetByIDParam{
		ID: param.BasicPriceID,
	})
	if err != nil {
		return 0, err
	}
	if basicPrice.ID == 0 {
		return 0, errors.New("契約タイプが見つかりません")
	}

	// Parse the price JSON
	var priceData []PriceItem
	if err := json.Unmarshal([]byte(basicPrice.PriceJson), &priceData); err != nil {
		return 0, fmt.Errorf("failed to parse price_json: %w", err)
	}

	// Calculate total expense
	var totalExpense float64

	// Generate hours from start to end (excluding end hour)
	currentTime := param.StartTime
	for currentTime.Before(param.EndTime) {
		hourStr := currentTime.Format("15:04")

		// Skip break time if provided
		if param.BreakTime != nil && currentTime.Hour() == param.BreakTime.Hour() && currentTime.Minute() == param.BreakTime.Minute() {
			currentTime = currentTime.Add(time.Hour)
			continue
		}

		// Find price for this hour
		for _, pi := range priceData {
			if pi.Hour == hourStr {
				totalExpense += pi.Price
				break
			}
		}

		currentTime = currentTime.Add(time.Hour)
	}

	return totalExpense, nil
}

// calculateAddFees calculates additional fees from daily report addition
func (uc *SiteReportUseCase) calculateAddFees(ctx context.Context, dailyReportAdditionID int64) (float64, float64, error) {
	// If no daily report addition ID provided, return 0 for both
	if dailyReportAdditionID == 0 {
		return 0, 0, nil
	}

	addition, err := uc.dailyReportAddition.GetByID(ctx, dailyreportadditionDmn.GetByIDParam{
		ID: dailyReportAdditionID,
	})
	if err != nil {
		return 0, 0, err
	}
	if addition.ID == 0 {
		return 0, 0, errors.New("日報追加が見つかりません")
	}

	return addition.AmountPerSite, addition.AmountPerHour, nil
}
