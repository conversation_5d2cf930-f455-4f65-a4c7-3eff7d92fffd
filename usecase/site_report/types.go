package sitereport

import (
	"time"

	sitereportworkerqualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker_qualification"
)

// GetListReq represents the request parameters for getting site report list
type GetListReq struct {
	StartDate string `query:"start_date"`
	EndDate   string `query:"end_date"`

	ParsedStartDate time.Time
	ParsedEndDate   time.Time
}

// GetListResp represents the response structure for site report list
type GetListResp struct {
	Year  string      `json:"year"`
	Month []MonthData `json:"month"`
}

type MonthData struct {
	Value       string     `json:"value"`
	Worker      int64      `json:"worker"`
	TotalAmount float64    `json:"total_amount"`
	Date        []DateData `json:"date"`
}

type DateData struct {
	Value       string       `json:"value"`
	Worker      int64        `json:"worker"`
	TotalAmount float64      `json:"total_amount"`
	Report      []ReportData `json:"report"`
}

type ReportData struct {
	SiteReportID int64   `json:"site_report_id"`
	SiteName     string  `json:"site_name"`
	Worker       int64   `json:"worker"`
	TotalAmount  float64 `json:"total_amount"`
	BStartTime   string  `json:"b_start_time"`
	BEndTime     string  `json:"b_end_time"`
	Note         string  `json:"note"`
	IsLocked     bool    `json:"is_locked"`
}

// BulkUpdateReq represents the request structure for bulk updating site reports
type BulkUpdateReq struct {
	SiteReportIDs   []int64 `json:"site_report_ids"`
	WorkDate        *string `json:"work_date,omitempty"`         // Optional, format: YYYY-MM-DD
	IsLocked        *bool   `json:"is_locked,omitempty"`         // Optional boolean
	IsInvoiceIssued *bool   `json:"is_invoice_issued,omitempty"` // Optional boolean

	UserRoles []string
}

// GetStatutoryCalculationVariableReq represents the request parameters for getting statutory calculation variables
type GetStatutoryCalculationVariableReq struct {
	CustomerID            int64  `query:"customer_id"`
	BasicPriceID          int64  `query:"basic_price_id"`
	StartTime             string `query:"start_time"`
	EndTime               string `query:"end_time"`
	BreakTime             string `query:"break_time"`
	DailyReportAdditionID int64  `query:"daily_report_addition_id"`

	// Parsed fields for internal use
	ParsedStartTime time.Time
	ParsedEndTime   time.Time
	ParsedBreakTime *time.Time
}

// GetStatutoryCalculationVariableResp represents the response structure for statutory calculation variables
type GetStatutoryCalculationVariableResp struct {
	StatutoryRate    float64 `json:"statutory_rate"`
	ExpensePerWorker float64 `json:"expense_per_worker"`
	AddFeePerSite    float64 `json:"add_fee_per_site"`
	AddFeePerWorker  float64 `json:"add_fee_per_worker"`
}

type PriceItem struct {
	Hour  string  `json:"hour"`
	Price float64 `json:"price"`
}

type CalculateExpensePerWorker struct {
	BasicPriceID int64
	StartTime    time.Time
	EndTime      time.Time
	BreakTime    *time.Time
}

// GetDetailListReq represents the request parameters for getting site report detail list
type GetDetailListReq struct {
	WorkDate string `query:"work_date"`
	ID       int64  `query:"id"`

	ParsedWorkDate time.Time
}

// GetDetailListResp represents the response structure for site report detail list
type GetDetailListResp struct {
	ID                           int64                       `json:"id"`
	WorkDate                     string                      `json:"work_date"`
	IsLocked                     bool                        `json:"is_locked"`
	SiteName                     string                      `json:"site_name"`
	CustomerID                   int64                       `json:"customer_id"`
	CustomerName                 string                      `json:"customer_name"`
	HasStatutory                 bool                        `json:"has_statutory"`
	DepartmentID                 int64                       `json:"department_id"`
	DepartmentName               string                      `json:"department_name"`
	DepartmentPicID              int64                       `json:"department_pic_id"`
	DepartmentPicName            string                      `json:"department_pic_name"`
	BillDate                     string                      `json:"bill_date"`
	BasicPriceID                 int64                       `json:"basic_price_id"`
	BasicPriceName               string                      `json:"basic_price_name"`
	DailyReportAdditionID        int64                       `json:"daily_report_addition_id"`
	DailyReportAdditionName      string                      `json:"daily_report_addition_name"`
	DailyReportAdditionPerSite   float64                     `json:"daily_report_addition_per_site"`
	DailyReportAdditionPerWorker float64                     `json:"daily_report_addition_per_worker"`
	TotalWorker                  int64                       `json:"total_worker"`
	DistrictBlockID              int64                       `json:"district_block_id"`
	DistrictBlockName            string                      `json:"district_block_name"`
	DistrictBlockUnit            int64                       `json:"district_block_unit"`
	TransitPlaceBlockID          int64                       `json:"transit_place_block_id"`
	TransitPlaceBlockName        string                      `json:"transit_place_block_name"`
	TransitPlaceBlockUnit        int64                       `json:"transit_place_block_unit"`
	BStartTime                   string                      `json:"b_start_time"`
	BEndTime                     string                      `json:"b_end_time"`
	SStartTime                   string                      `json:"s_start_time"`
	SEndTime                     string                      `json:"s_end_time"`
	BreakTime                    string                      `json:"break_time"`
	LateEarlyWorker              int64                       `json:"late_early_worker"`
	ExtraTimeCharge              []ExtraTimeChargeItem       `json:"extra_time_charge"`
	Note                         string                      `json:"note"`
	NoteForInvoice               string                      `json:"note_for_invoice"`
	TotalAmount                  float64                     `json:"total_amount"`
	Statutory                    *GetDetailListStatutoryResp `json:"statutory"`
	Option                       []GetDetailListOptionResp   `json:"option"`
	Worker                       []GetDetailListWorkerResp   `json:"worker"`
}

type ExtraTimeChargeItem struct {
	Time        string `json:"time"`
	TotalWorker int64  `json:"total_worker"`
}

type GetDetailListStatutoryResp struct {
	ID               int64   `json:"id"`
	Rate             float64 `json:"rate"`
	ExpensePerWorker float64 `json:"expense_per_worker"`
	Adjustment       float64 `json:"adjustment"`
	TotalAmount      float64 `json:"total_amount"`
	Note             string  `json:"note"`
}

type GetDetailListOptionResp struct {
	ID     int64   `json:"id"`
	Name   string  `json:"name"`
	Amount float64 `json:"amount"`
}

type GetDetailListWorkerResp struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

// WorkerCalculationReq represents the request parameters for worker calculation
type WorkerCalculationReq struct {
	BasicPriceID              int64   `json:"basic_price_id"`
	UserID                    int64   `json:"user_id"`
	StartTime                 string  `json:"start_time"`
	EndTime                   string  `json:"end_time"`
	BreakTime                 string  `json:"break_time"`
	TransportExpense          float64 `json:"transport_expense"`
	LeaderAllowance           float64 `json:"leader_allowance"`
	DistantFeeID              int64   `json:"distant_fee_id"`
	QualificationAllowanceIDs []int64 `json:"qualification_allowance_ids"`

	// Parsed fields for internal use
	ParsedStartTime time.Time
	ParsedEndTime   time.Time
	ParsedBreakTime *time.Time
}

// WorkerCalculationResp represents the response structure for worker calculation
type WorkerCalculationResp struct {
	TotalAmount         float64 `json:"total_amount"`
	TaxAmount           float64 `json:"tax_amount"`
	TotalAmountAfterTax float64 `json:"total_amount_after_tax"`
}

type DoWorkerCalculationResp struct {
	TotalAmount         float64
	TaxIncomeID         int64
	TaxAmount           float64
	TotalAmountAfterTax float64
	DistantFee          float64
	Qualifications      []Qualification
}

type CalculateQualificationFeeResp struct {
	TotalFee       float64
	Qualifications []Qualification
}

type Qualification struct {
	ID       int64
	Title    string
	AddClaim float64
}

type CalculateTaxAmountResp struct {
	IncomeTaxID int64
	Amount      float64
}

// SaveWorkerReq represents the request parameters for saving site report worker
type SaveWorkerReq struct {
	ID                        int64   `json:"id"`
	UserID                    int64   `json:"user_id"`
	BasicPriceID              int64   `json:"basic_price_id"`
	StartTime                 string  `json:"start_time"`
	EndTime                   string  `json:"end_time"`
	BreakTime                 string  `json:"break_time"`
	TransportExpense          float64 `json:"transport_expense"`
	LeaderAllowance           float64 `json:"leader_allowance"`
	DistantFeeID              int64   `json:"distant_fee_id"`
	QualificationAllowanceIDs []int64 `json:"qualification_allowance_ids"`

	// Parsed fields for internal use
	ParsedStartTime time.Time
	ParsedEndTime   time.Time
	ParsedBreakTime *time.Time
}

type DoUpdateWorkerParam struct {
	Req                SaveWorkerReq
	CalcResp           DoWorkerCalculationResp
	WorkerSnapshotJSON string
}

type ManageWorkerQualificationsParam struct {
	WorkerID                  int64
	RequestedQualificationIDs []int64
	CalculatedQualifications  []Qualification
}

type ProcessQualificationChangesParam struct {
	WorkerID         int64
	ExistingQualMap  map[int64]bool
	RequestedQualMap map[int64]bool
	QualDataMap      map[int64]Qualification
}

type ExecuteBulkQualificationOperationsParam struct {
	WorkerID int64
	ToUpdate []sitereportworkerqualificationDmn.QualificationUpdateData
	ToInsert []sitereportworkerqualificationDmn.QualificationData
	ToDelete []int64
}

type CalculateTaxAmountParam struct {
	TotalAmount float64
	UserID      int64
}
