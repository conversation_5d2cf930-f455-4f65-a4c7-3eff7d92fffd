package sitereport

import "time"

type SiteReport struct {
	ID                    int64      `gorm:"column:id;primary_key"`
	WorkDate              time.Time  `gorm:"column:work_date"`
	SiteName              string     `gorm:"column:site_name"`
	DepartmentPicID       int64      `gorm:"column:department_pic_id"`
	HasStatutory          bool       `gorm:"column:has_statutory"`
	BillDate              time.Time  `gorm:"column:bill_date"`
	BasicPriceID          int64      `gorm:"column:basic_price_id"`
	DailyReportAdditionID int64      `gorm:"column:daily_report_addition_id"`
	Worker                int64      `gorm:"column:worker"`
	DistrictBlockID       int64      `gorm:"column:district_block_id"`
	DistrictBlockUnit     int64      `gorm:"column:district_block_unit"`
	TransitPlaceBlockID   int64      `gorm:"column:transit_place_block_id"`
	TransitPlaceUnit      int64      `gorm:"column:transit_place_unit"`
	BStartTime            LocalTime  `gorm:"column:b_start_time"`
	BEndTime              LocalTime  `gorm:"column:b_end_time"`
	SStartTime            LocalTime  `gorm:"column:s_start_time"`
	SEndTime              LocalTime  `gorm:"column:s_end_time"`
	BreakTime             LocalTime  `gorm:"column:break_time"`
	LateEarlyWorker       int64      `gorm:"column:late_early_worker"`
	ExtraTimeCharge       string     `gorm:"column:extra_time_charge;type:json"`
	Note                  string     `gorm:"column:note"`
	NoteForInvoice        string     `gorm:"column:note_for_invoice"`
	TotalAmount           float64    `gorm:"column:total_amount"`
	ConsumptionTaxID      int64      `gorm:"column:consumption_tax_id"`
	Snapshot              string     `gorm:"column:snapshot;type:json"`
	IsLocked              bool       `gorm:"column:is_locked"`
	IsInvoiceIssued       bool       `gorm:"column:is_invoice_issued"`
	CreatedBy             int64      `gorm:"column:created_by"`
	CreatedAt             time.Time  `gorm:"column:created_at"`
	UpdatedAt             time.Time  `gorm:"column:updated_at"`
	DeletedAt             *time.Time `gorm:"column:deleted_at"`

	// TODO will uncomment when related table model is created
	// DepartmentPic departmentpic.DepartmentPic `gorm:"foreignkey:DepartmentPicID"`
	// BasicPrice basicprice.BasicPrice `gorm:"foreignkey:BasicPriceID"`
	// DailyReportAddition dailyreportaddition.DailyReportAddition `gorm:"foreignkey:DailyReportAdditionID"`
	// DistrictBlock districtblock.DistrictBlock `gorm:"foreignkey:DistrictBlockID"`
	// TransitPlaceBlock transitplaceblock.TransitPlaceBlock `gorm:"foreignkey:TransitPlaceBlockID"`
	// ConsumptionTax consumptiontax.ConsumptionTax `gorm:"foreignkey:ConsumptionTaxID"`
	// User user.User `gorm:"foreignkey:CreatedBy"`
}

type Snapshot struct {
	CustomerID                   int64   `json:"customer_id"`
	CustomerName                 string  `json:"customer_name"`
	DepartmentID                 int64   `json:"department_id"`
	DepartmentName               string  `json:"department_name"`
	DepartmentPicName            string  `json:"department_pic_name"`
	BasicPriceName               string  `json:"basic_price_name"`
	DailyReportAdditionName      string  `json:"daily_report_addition_name"`
	DailyReportAdditionPerSite   float64 `json:"daily_report_addition_per_site"`
	DailyReportAdditionPerWorker float64 `json:"daily_report_addition_per_worker"`
	DistrictBlockName            string  `json:"district_block_name"`
	TransitPlaceBlockName        string  `json:"transit_place_block_name"`
}

// GetListParam represents the parameters for getting site report list
type GetListParam struct {
	StartDate time.Time
	EndDate   time.Time
}

// BulkUpdateParam represents the parameters for bulk updating site reports
type BulkUpdateParam struct {
	SiteReportIDs   []int64
	WorkDate        *time.Time
	IsLocked        *bool
	IsInvoiceIssued *bool
}

// GetDetailListParam represents the parameters for getting site report detail list
type GetDetailListParam struct {
	WorkDate *time.Time
	ID       *int64
}
