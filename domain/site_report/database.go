package sitereport

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getList fetches site reports filtered by date range.
func (rsc SiteReportResource) getList(ctx context.Context, param GetListParam) ([]SiteReport, error) {
	var reports []SiteReport

	db := dbmanager.Manager().WithContext(ctx)

	// Filter by work_date range and exclude soft deleted records
	err := db.Where("work_date >= ? AND work_date <= ?", param.StartDate, param.EndDate).
		Where("deleted_at IS NULL").
		Order("work_date ASC, id ASC").
		Find(&reports).Error

	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}

	return reports, nil
}

// bulkUpdate updates multiple site reports with the specified fields.
func (rsc SiteReportResource) bulkUpdate(ctx context.Context, param BulkUpdateParam) error {
	db := dbmanager.Manager().WithContext(ctx)

	// Build update map with only the fields that are provided
	updateFields := make(map[string]interface{})

	if param.WorkDate != nil {
		updateFields["work_date"] = *param.WorkDate
	}

	if param.IsLocked != nil {
		updateFields["is_locked"] = *param.IsLocked
	}

	if param.IsInvoiceIssued != nil {
		updateFields["is_invoice_issued"] = *param.IsInvoiceIssued
	}

	// Perform bulk update for the specified site report IDs
	err := db.Model(&SiteReport{}).
		Where("id IN ?", param.SiteReportIDs).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getDetailList fetches site reports with detailed information filtered by work_date or id.
func (rsc SiteReportResource) getDetailList(ctx context.Context, param GetDetailListParam) ([]SiteReport, error) {
	var reports []SiteReport

	db := dbmanager.Manager().WithContext(ctx)

	// Build query with filters
	query := db.Where("deleted_at IS NULL")

	if param.WorkDate != nil {
		query = query.Where("work_date = ?", *param.WorkDate)
	}

	if param.ID != nil {
		query = query.Where("id = ?", *param.ID)
	}

	// Use GORM Preload to eagerly load related data in a single query
	// This will create JOINs to fetch all related data at once
	query = query.
		Preload("DepartmentPic.Department.Customer").
		Preload("BasicPrice").
		Preload("DailyReportAddition").
		Preload("DistrictBlock").
		Preload("TransitPlaceBlock")

	// Execute query
	err := query.Order("id ASC").Find(&reports).Error
	if err != nil {
		return []SiteReport{}, log.LogError(err, nil)
	}

	return reports, nil
}
